<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="164f5203-ec2b-40a4-b2a2-066297b7c96a" name="更改" comment="测试nginx配置">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/myapp/config/proxy.ts" beforeDir="false" afterPath="$PROJECT_DIR$/myapp/config/proxy.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/myapp/src/services/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/myapp/src/services/api/index.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2wp0A4wAFOsGr1sFQEsca2FS6L3" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;/Users/<USER>/sourceCode/evermodel/dev/rulesengineplatform2/myapp/node_modules/prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;settings.javascript.linters.eslint&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Users/<USER>/sourceCode/evermodel/dev/rulesengineplatform2/myapp/node_modules/typescript/lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="164f5203-ec2b-40a4-b2a2-066297b7c96a" name="更改" comment="" />
      <created>1746723062048</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1746723062048</updated>
      <workItem from="1746723063802" duration="44000" />
      <workItem from="1746723186820" duration="207000" />
      <workItem from="1746843505414" duration="61000" />
      <workItem from="1746843588399" duration="600000" />
      <workItem from="1747016333069" duration="8023000" />
      <workItem from="1747189182453" duration="5298000" />
    </task>
    <task id="LOCAL-00001" summary="新增 Dockerfile">
      <option name="closed" value="true" />
      <created>1746784916080</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1746784916080</updated>
    </task>
    <task id="LOCAL-00002" summary="测试nginx配置">
      <option name="closed" value="true" />
      <created>1747038898447</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747038898447</updated>
    </task>
    <task id="LOCAL-00003" summary="测试nginx配置2">
      <option name="closed" value="true" />
      <created>1747040296607</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747040296607</updated>
    </task>
    <task id="LOCAL-00004" summary="测试nginx配置">
      <option name="closed" value="true" />
      <created>1747191944924</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1747191944924</updated>
    </task>
    <option name="localTasksCounter" value="5" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="新增 Dockerfile" />
    <MESSAGE value="测试nginx配置2" />
    <MESSAGE value="测试nginx配置" />
    <option name="LAST_COMMIT_MESSAGE" value="测试nginx配置" />
  </component>
</project>