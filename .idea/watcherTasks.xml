<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectTasksOptions">
    <TaskOptions isEnabled="true">
      <option name="arguments" value="$FileName$ $FileNameWithoutExtension$.css --source-map" />
      <option name="checkSyntaxErrors" value="true" />
      <option name="description" />
      <option name="exitCodeBehavior" value="ERROR" />
      <option name="fileExtension" value="less" />
      <option name="immediateSync" value="true" />
      <option name="name" value="Less" />
      <option name="output" value="$FileNameWithoutExtension$.css:$FileNameWithoutExtension$.css.map" />
      <option name="outputFilters">
        <array>
          <FilterInfo>
            <option name="description" value="" />
            <option name="name" value="" />
            <option name="regExp" value="$MESSAGE$\Q in \E$FILE_PATH$\Q on line \E$LINE$\Q, column \E$COLUMN$" />
          </FilterInfo>
        </array>
      </option>
      <option name="outputFromStdout" value="false" />
      <option name="program" value="lessc" />
      <option name="runOnExternalChanges" value="true" />
      <option name="scopeName" value="Project Files" />
      <option name="trackOnlyRoot" value="true" />
      <option name="workingDir" value="$FileDir$" />
      <envs />
    </TaskOptions>
  </component>
</project>