import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, PersonalCenter } from '@/components';
import { currentUser as queryCurrentUser } from '@/services/ant-design-pro/api';
import type { API } from '@/services/ant-design-pro/typings';
import { LinkOutlined } from '@ant-design/icons';
import type { Settings as LayoutSettings } from '@ant-design/pro-components';
import { SettingDrawer } from '@ant-design/pro-components';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history, Link } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import { errorConfig } from './requestErrorConfig';
const isDev = process.env.NODE_ENV === 'development';
const loginPath = '/user/login';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
  collapsed?: boolean;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await queryCurrentUser({
        skipErrorHandler: true,
      });
      return msg.data;
    } catch (error) {
      // 注释掉错误时跳转登录页的逻辑
      // history.push(loginPath);
      return {
        name: '模拟用户',
        avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
      } as API.CurrentUser;
    }
  };

  // 直接返回模拟用户数据，跳过登录验证
  return {
    fetchUserInfo,
    currentUser: {
      name: '模拟用户',
      avatar: 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png',
    },
    settings: defaultSettings as Partial<LayoutSettings>,
    collapsed: false,
  };
}

export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    collapsed: initialState?.collapsed,
    onCollapse: (collapsed: boolean) => {
      setInitialState((preInitialState) => ({
        ...preInitialState,
        collapsed,
      }));
    },
    menuItemRender: (item) => {
      const isActive = item.path && location.pathname.startsWith(item.path);
      const iconSrc = isActive ? item.activeIcon : item.icon;
      if (initialState?.collapsed) {
        // 收缩时也用 Link 包裹 img，保证点击跳转和居中
        return iconSrc ? (
          <Link
            to={item.path || ''}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '40px',
            }}
          >
            <img
              src={iconSrc}
              style={{
                width: 24,
                height: 24,
                display: 'block',
                margin: '0 auto',
              }}
            />
          </Link>
        ) : null;
      }
      // 展开时显示图标+文本
      return (
        <Link to={item.path || ''} style={{ display: 'flex', alignItems: 'center' }}>
          {iconSrc && (
            <img
              src={iconSrc}
              style={{
                marginRight: 15,
                width: 24,
                height: 24,
                flexShrink: 0,
              }}
            />
          )}
          <span style={{ lineHeight: '24px', fontWeight: isActive ? '700' : 'normal' }}>
            {item.name}
          </span>
        </Link>
      );
    },
    actionsRender: () => [<PersonalCenter key="personal-center" />],  // 右上角的个人中心
    // avatarProps 已移至 PersonalCenter 组件中
    // avatarProps: {
    //   src: initialState?.currentUser?.avatar,
    //   title: <AvatarName />,
    //   render: (_, avatarChildren) => {
    //     return <AvatarDropdown>{avatarChildren}</AvatarDropdown>;
    //   },
    // },

    // 水印
    // waterMarkProps: {
    //   content: initialState?.currentUser?.name,
    // },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // 直接跳转到首页，如果访问的是登录页
      if (location.pathname === loginPath) {
        history.push('/Home');
      }
    },
    links: isDev
      ? [
          <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
            <LinkOutlined />
            <span>OpenAPI 文档</span>
          </Link>,
        ]
      : [],
    menuHeaderRender: (_, __, props) => {
      return <Header collapsed={props?.collapsed} />;
    },
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...errorConfig,
};
