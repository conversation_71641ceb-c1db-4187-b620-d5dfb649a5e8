// API 基础地址配置
// export const API_BASE_URL = 'http://192.168.191.21:8002';
export const API_BASE_URL = 'http://192.168.200.31:30002';

// API 路径配置
export const API_PATHS = {
  // 应用相关
  APP: {
    LIST: '/rule/apppage', // 获取应用列表
    DETAIL: '/rule/appdetail', // 获取应用详情
    CREATE: '/rule/appadd', // 创建应用
    UPDATE: '/rule/appupdate', // 更新应用
    DELETE: '/rule/appdelete', // 删除应用
  },
  // 用户相关
  USER: {
    LOGIN: '/api/login/account', // 登录
    LOGOUT: '/api/login/outLogin', // 退出登录
    CURRENT: '/api/currentUser', // 获取当前用户信息
  },
  // 规则相关
  RULE: {
    LIST: '/api/rule', // 获取规则列表
    CREATE: '/api/rule', // 创建规则
    UPDATE: '/api/rule', // 更新规则
    DELETE: '/api/rule', // 删除规则
  },
};

// 导出各模块API
export * from './request';
export * from './types';
export * as appApi from './app';
export * as modelApi from './model';
export * as componentApi from './component';
export * as mcpApi from './mcp';

// 兼容旧版
export const getApiUrl = (path: string, params?: Record<string, any>): string => {
  let url = `${API_BASE_URL}${path}`;

  if (params) {
    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    url = `${url}?${queryString}`;
  }

  return url;
};
