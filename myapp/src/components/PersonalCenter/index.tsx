import React from 'react';
import { Space, Divider } from 'antd';
import DataStats from './DataStats';
import MessageNotification from './MessageNotification';
import { AvatarDropdown } from '../RightContent/AvatarDropdown';
import { useModel } from '@umijs/max';
import './index.less';

const PersonalCenter: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  return (
    <div className="personal-center">
      <Space size={0} align="center" split={<Divider type="vertical" />}>
        {/* 数据统计 */}
        <DataStats />

        {/* 消息通知 */}
        <MessageNotification />

        {/* 用户头像和下拉菜单 */}
        <div className="user-avatar-section">
          <AvatarDropdown menu>
            <Space align="center" className="user-info">
              <img
                src={initialState?.currentUser?.avatar || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'}
                alt="avatar"
                className="user-avatar"
              />
              <span className="user-name">王爱国</span>
            </Space>
          </AvatarDropdown>
        </div>
      </Space>
    </div>
  );
};

export default PersonalCenter;
