import React from 'react';
import { Space } from 'antd';
import PersonalCenter from '../PersonalCenter';
import './index.less';

interface PageHeaderProps {
  title?: string;
  extra?: React.ReactNode;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, extra, className = '' }) => {
  return (
    <div className={`page-header ${className}`}>
      <div className="page-header-content">
        {title && (
          <div className="page-header-title">
            <h2>{title}</h2>
          </div>
        )}
        {extra && (
          <div className="page-header-extra">
            {extra}
          </div>
        )}
        <div className="page-header-actions">
          <PersonalCenter />
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
