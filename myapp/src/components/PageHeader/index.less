.page-header {
  position: relative;
  z-index: 10;
  background: transparent;
  padding: 16px 20px;
  border-bottom: 1px solid #EAEFF3;

  .page-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 48px;

    .page-header-title {
      flex: 1;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .page-header-extra {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .page-header-actions {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

// 当没有标题和额外内容时，个人中心居右显示
.page-header .page-header-content:has(.page-header-title:empty) .page-header-actions,
.page-header .page-header-content:not(:has(.page-header-title)) .page-header-actions {
  flex: none;
  margin-left: auto;
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;

    .page-header-content {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .page-header-title,
      .page-header-extra,
      .page-header-actions {
        flex: none;
      }

      .page-header-actions {
        justify-content: center;
      }
    }
  }
}
