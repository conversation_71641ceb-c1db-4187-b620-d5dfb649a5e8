import React from 'react';
import { Mo<PERSON>, Button } from 'antd';
import './DeleteConfirmModal.less';

interface DeleteConfirmModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: () => void;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({ visible, onCancel, onOk }) => {
  return (
    <Modal
      open={visible}
      footer={null}
      onCancel={onCancel}
      centered
      className="delete-confirm-modal"
      width={360}
      closable
    >
      <div className="delete-modal-gradient-bar"></div>
      <img src="/icons/delete_warrning.svg" alt="删除警告" className="delete-modal-img" />
      <div className="delete-modal-title">是否确认删除?</div>
      <div className="delete-modal-btns">
        <Button onClick={onCancel} className="delete-modal-cancel">取消</Button>
        <Button type="primary" danger onClick={onOk} className="delete-modal-ok">确定</Button>
      </div>
    </Modal>
  );
};

export default DeleteConfirmModal; 