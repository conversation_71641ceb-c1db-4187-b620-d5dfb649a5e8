import { <PERSON><PERSON>ontainer } from '@ant-design/pro-components';
// import { PlusOutlined } from '@ant-design/icons';
import { modelApi } from '@/services/api';
import { Avatar, Button, Col, message, Modal, Row, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';
import { ModelCard, PageHeader } from '@/components';
import CategoryFilter from '@/components/CategoryFilter';
import SearchActionBar from '@/components/SearchActionBar';

interface CategoryItem {
  id: string;
  name: string;
  icon: string;
  children: CategoryItem[] | null;
}

// 更新 ModelItem 接口
interface ModelItem {
  id: string;
  name: string;
  description: string;
  icon?: string;
  type: string;
  subType: string;
  isOnline: number;
  parameters?: string;
  version?: string;
  createdTime?: string;
  updatedTime?: string;
}

const ModelList: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(12);
  const [loading, setLoading] = useState<boolean>(false);
  const [detailLoading, setDetailLoading] = useState<boolean>(false);
  const [modelList, setModelList] = useState<ModelItem[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedSubType, setSelectedSubType] = useState<string>('');
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [currentModel, setCurrentModel] = useState<ModelItem | null>(null);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);

  const fetchCategories = async () => {
    try {
      const data = await modelApi.getModelCategories();
      setCategories(data || []);
    } catch (error: any) {
      console.error('获取分类列表出错:', error);
      message.error('获取分类列表失败：' + (error.message || '未知错误'));
    }
  };

  const fetchModelList = async () => {
    setLoading(true);
    try {
      const { data, total } = await modelApi.getModelList({
        keywords: searchKeyword || undefined,
        id: selectedSubType || selectedType || undefined,
      });
      setModelList(data);
      setTotal(total);
    } catch (error: any) {
      console.error('获取模型列表出错:', error);
      message.error('获取模型列表失败：' + (error.message || '未知错误'));
      setModelList([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchModelList();
  }, [currentPage, pageSize, selectedType, selectedSubType, searchKeyword]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDetailClick = async (model: ModelItem) => {
    try {
      setDetailLoading(true);
      const detail = await modelApi.getModelDetail(model.id);
      setCurrentModel({
        ...model,
        ...detail,
      });
      setDetailVisible(true);
    } catch (error) {
      message.error('获取详情失败');
    } finally {
      setDetailLoading(false);
    }
  };

  const handleDetailClose = () => {
    setDetailVisible(false);
    setCurrentModel(null);
  };

  const handleStatusChange = async (id: string, checked: boolean) => {
    try {
      await modelApi.updateModelStatus({
        id,
        isOnline: checked ? 1 : 0,
      });
      message.success(`${checked ? '已上架' : '未上架'}`);
      // 直接获取最新列表，不显示加载状态
      const { data, total } = await modelApi.getModelList({
        keywords: searchKeyword || undefined,
        id: selectedSubType || selectedType || undefined,
      });
      setModelList(data);
      setTotal(total);
    } catch (error: any) {
      message.error('' + (error.message || '未知错误'));
    }
  };

  const currentCategory = categories.find((category) => category.id === selectedType);

  // 查询、重置、回车事件
  const handleQuery = () => {
    setCurrentPage(1);
    fetchModelList();
  };
  const handleReset = () => {
    setSearchKeyword('');
    setSelectedType('');
    setSelectedSubType('');
    setCurrentPage(1);
  };
  const handlePressEnter = () => {
    setCurrentPage(1);
    fetchModelList();
  };

  return (
    <PageContainer>
      {/* 页面顶部个人中心 */}
      <PageHeader />

      {/* 新筛选区域，放在页面顶部 */}
      <CategoryFilter
        categories={categories}
        selectedType={selectedType}
        selectedSubType={selectedSubType}
        onTypeChange={(typeId) => {
          setSelectedType(typeId);
          setSelectedSubType('');
          setCurrentPage(1);
        }}
        onSubTypeChange={(subTypeId) => {
          setSelectedSubType(subTypeId);
          setCurrentPage(1);
        }}
      />
      <SearchActionBar
        searchKeyword={searchKeyword}
        onKeywordChange={setSearchKeyword}
        onQuery={handleQuery}
        onReset={handleReset}
        onPressEnter={handlePressEnter}
        showStatus={false}
      />
      <Row gutter={[16, 16]} style={{marginTop:'20px'}}>
        {loading ? (
          <Col span={24}>
            <div style={{ padding: '50px', textAlign: 'center' }}>
              <Spin />
              <div style={{ marginTop: 16 }}>加载中...</div>
            </div>
          </Col>
        ) : (
          <>
            {modelList.map((model: ModelItem, index: number) => (
              <Col xs={24} sm={24} md={12} lg={12} key={model.id || index}>
                <ModelCard
                  id={model.id}
                  name={model.name}
                  description={model.description}
                  icon={model.icon}
                  isOnline={model.isOnline}
                  onDetailClick={() => handleDetailClick(model)}
                  onStatusChange={(checked: boolean) => handleStatusChange(model.id, checked)}
                />
              </Col>
            ))}
          </>
        )}
      </Row>

      <Modal
        title="详情"
        open={detailVisible}
        onCancel={handleDetailClose}
        footer={[
          <Button key="close" onClick={handleDetailClose}>
            确定
          </Button>,
        ]}
        width={600}
      >
        {detailLoading ? (
          <div style={{ padding: '50px', textAlign: 'center' }}>
            <Spin />
            <div style={{ marginTop: 16 }}>加载中...</div>
          </div>
        ) : currentModel ? (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>类型：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {currentCategory?.name || '全部'}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>名称：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {currentModel.name}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>图标：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                <Avatar src={currentModel.icon} size={48} />
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>参数：</div>
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  minHeight: '100px',
                }}
              >
                {currentModel.parameters || currentModel.description}
              </div>
            </div>
          </div>
        ) : null}
      </Modal>
      {total > 0 && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            &lt;
          </Button>
          {Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => (
            <Button
              key={i + 1}
              type={currentPage === i + 1 ? 'primary' : 'default'}
              style={{ margin: '0 8px' }}
              onClick={() => handlePageChange(i + 1)}
            >
              {i + 1}
            </Button>
          ))}
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === Math.ceil(total / pageSize)}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            &gt;
          </Button>
        </div>
      )}
    </PageContainer>
  );
};

export default ModelList;
