import CategoryFilter from '@/components/CategoryFilter';
import DeleteConfirmModal from '@/components/DeleteConfirmModal/DeleteConfirmModal';
import SearchActionBar from '@/components/SearchActionBar';
import { appApi } from '@/services/api';
import type { AppItem } from '@/services/api/types';
import { CalendarOutlined, CopyOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Avatar, Button, Card, Dropdown, Menu, Spin, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import CreateAppModal from './CreateAppModal/CreateAppModal';
import EditAppModal from './EditAppModal';
import './Home.less';

const CardList: React.FC = () => {
  const { setInitialState } = useModel('@@initialState');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const cardsPerRow = screenWidth >= 1600 ? 5 : 4;
  const [pageSize, setPageSize] = useState<number>(screenWidth >= 1600 ? 14 : 11);
  const [appType, setAppType] = useState<string>(''); // 修改初始值为空字符串，代表"全部
  const [loading, setLoading] = useState<boolean>(false);
  const [appList, setAppList] = useState<AppItem[]>([]);
  const [hasError, setHasError] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [currentApp, setCurrentApp] = useState<AppItem>();
  const [showManage, setShowManage] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [appStatus, setAppStatus] = useState<string | undefined>(undefined);
  const [iframeLoading, setIframeLoading] = useState<boolean>(false);

  const fetchAppList = async () => {
    setLoading(true);
    try {
      const { data, total } = await appApi.getAppList({
        page: currentPage,
        pageSize,
        type: appType !== 'all' ? appType : undefined,
        keywords: searchKeyword || undefined,
        appStatus: appStatus,
      });
      setAppList(data);
      setTotal(total);
    } catch (error: unknown) {
      console.error('获取应用列表出错:', error);
      message.error('获取应用列表失败');
      setHasError(true);
      setAppList([]);
    } finally {
      setLoading(false);
    }
  };

  const handleQuery = () => {
    setCurrentPage(1);
    fetchAppList();
  };

  const handleReset = () => {
    setSearchKeyword('');
    setAppStatus(undefined);
    setAppType('');
    setCurrentPage(1);
  };

  const handlePressEnter = () => {
    setCurrentPage(1);
    fetchAppList();
  };

  useEffect(() => {
    fetchAppList();
  }, [currentPage, pageSize, appType]);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setScreenWidth(width);
      setPageSize(width >= 1600 ? 14 : 11);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 监听 iframe 页面的 postMessage 消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'NAVIGATE_TO_HOME') {
        window.location.href = '/home';
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateApp = async (formData: FormData) => {
    try {
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const type = formData.get('type') as string;
      const file = formData.get('file') as File;

      await appApi.createApp({
        name,
        description,
        type,
        file,
      });
      message.success('创建应用成功');
      setCreateModalVisible(false);
      fetchAppList();
    } catch (error) {
      message.error('创建应用失败');
    }
  };

  const handleEditApp = async (formData: FormData) => {
    if (!currentApp?.id) {
      message.error('缺少必要参数');
      return;
    }

    try {
      const name = formData.get('name') as string;
      const description = formData.get('description') as string;
      const type = formData.get('type') as string;
      const file = formData.get('file') as File;
      const fileDelete = formData.get('fileDelete') as string;

      await appApi.updateApp({
        id: currentApp.id,
        name,
        description,
        type,
        flowId: currentApp.flowId,
        file,
        fileDelete: parseInt(fileDelete || '0', 10), // 将字符串转换为数字
      });
      message.success('修改成功');
      setEditModalVisible(false);
      fetchAppList();
    } catch (error) {
      console.error('修改应用失败:', error);
      message.error('修改失败');
    }
  };

  const handleDeleteApp = async () => {
    try {
      if (!currentApp?.id) {
        message.error('缺少必要参数');
        return;
      }

      await appApi.deleteApp(currentApp.id);
      message.success('删除应用成功');
      setDeleteModalVisible(false);
      fetchAppList();
    } catch (error) {
      message.error('删除应用失败');
    }
  };

  if (showManage) {
    // 本地地址(dev分之支本地==>测试使用)
    // const iframeUrl = `http://localhost:3002/flow/${currentApp?.flowId}?type=${currentApp?.type}`;
    //  线上地址 强哥的地址（无汉化）
    // const iframeUrl = `http://**************:7860/flow/${currentApp?.flowId}?type=${currentApp?.type}`;
    //  线上地址（已汉化==>正式使用）
    const iframeUrl = `http://**************:3002/flow/${currentApp?.flowId}?type=${currentApp?.type}`;
    return (
      <div style={{ width: '100%', height: '100vh', position: 'relative' }}>
        {iframeLoading && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'rgba(255, 255, 255, 0.8)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              zIndex: 1000,
            }}
          >
            <Spin size="large" />
          </div>
        )}
        <iframe
          src={iframeUrl}
          style={{ width: '100%', height: '100%', border: 'none', padding: '0' }}
          title="管理页面"
          onLoad={() => setIframeLoading(false)}
        />
      </div>
    );
  }

  // 分类数据（应用页面只有一级分类）
  const appCategories = [
    { id: '1', name: '智能体', icon: '', children: null },
    { id: '2', name: '工作流', icon: '', children: null },
  ];

  return (
    <PageContainer>
      {/* <Breadcrumb
        items={[{ title: '应用' }, { title: '应用列表' }]}
        style={{ marginBottom: '16px' }}
      /> */}
      {/* 分类筛选 */}
      <CategoryFilter
        categories={appCategories}
        selectedType={appType}
        selectedSubType={''}
        onTypeChange={(typeId) => {
          setAppType(typeId);
          setCurrentPage(1);
        }}
        onSubTypeChange={() => {}}
      />
      {/* 搜索与操作栏 */}
      <SearchActionBar
        searchKeyword={searchKeyword}
        onKeywordChange={setSearchKeyword}
        onQuery={handleQuery}
        onReset={handleReset}
        onPressEnter={handlePressEnter}
        appStatus={appStatus}
        onStatusChange={setAppStatus}
        showStatus={true}
      />
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', marginTop: '20px' }}>
        {loading ? (
          <div style={{ width: '100%', padding: '50px', textAlign: 'center' }}>
            <Spin />
            <div style={{ marginTop: 16 }}>加载中...</div>
          </div>
        ) : hasError ? (
          <div style={{ width: '100%', padding: '50px', textAlign: 'center' }}>
            加载失败，请重试
          </div>
        ) : !appList?.length ? (
          // 临时修改为
          // true ? ( // 强制显示缺省状态
          // 缺省状态：当没有应用时显示
          <div
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '400px',
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ marginBottom: '24px' }}>
                <img
                  src="/icons/default.svg"
                  alt="缺省状态"
                  style={{ width: '120px', height: '120px' }}
                />
              </div>
              <div style={{ color: '#8C8C8C', fontSize: '16px', marginBottom: '30px' }}>
                还没有应用呢，快点击新增一个吧～
              </div>
              <Button
                type="primary"
                icon={
                  <img
                    src="/icons/add.svg"
                    alt="新增"
                    style={{ width: '16px', height: '16px', marginRight: '4px' }}
                  />
                }
                onClick={() => setCreateModalVisible(true)}
                style={{ backgroundColor: '#00B96B', borderColor: '#00B96B' }}
              >
                新增应用
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div style={{ width: `calc(${100 / cardsPerRow}% - 10px)` }}>
              <Card
                hoverable
                style={{
                  width: '100%',
                  height: '168px',
                  border: '1px dashed #d9d9d9',
                  background: 'transparent',
                  borderRadius: '2px',
                }}
                styles={{
                  body: {
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    padding: 0,
                  },
                }}
                className="add-card"
                onClick={() => setCreateModalVisible(true)}
              >
                <div
                  className="add-card-content"
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    width: '100%',
                    background: '#F6F7FB',
                    padding: '16px',
                    boxSizing: 'border-box',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      width: '48px',
                      height: '48px',
                      borderRadius: '50%',
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                    }}
                  >
                    <PlusOutlined style={{ fontSize: '24px', color: '#8c8c8c' }} />
                  </div>
                  <p style={{ marginTop: '16px', color: '#8c8c8c' }}>新增应用</p>
                </div>
              </Card>
            </div>
            {appList.map((app: AppItem, index: number) => (
              <div style={{ width: `calc(${100 / cardsPerRow}% - 10px)` }} key={app.id || index}>
                <Card
                  hoverable
                  style={{
                    width: '100%',
                    height: '168px',
                    position: 'relative',
                    background: '#F6F7FB',
                  }}
                  onClick={() => {
                    setInitialState((prev) => ({
                      ...prev,
                      collapsed: !prev?.collapsed,
                    }));
                    setIframeLoading(true);
                    setCurrentApp(app);
                    setShowManage(true);
                  }}
                  actions={[
                    <a
                      key="edit"
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentApp(app);
                        setEditModalVisible(true);
                      }}
                      style={{
                        height: '40px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#191919',
                        fontSize: 12,
                      }}
                    >
                      <img
                        src="/icons/edit.svg"
                        alt="编辑"
                        style={{
                          width: 16,
                          height: 16,
                          marginRight: 4,
                          verticalAlign: 'text-bottom',
                          margin: '0 5px',
                        }}
                      />
                      编辑
                    </a>,
                    <Dropdown
                      key="more"
                      overlay={
                        <Menu
                          onClick={async ({ key, domEvent }) => {
                            domEvent.stopPropagation();
                            if (key === 'copy') {
                              try {
                                await appApi.duplicateApp(app.id);
                                message.success('复制成功');
                                fetchAppList();
                              } catch (error) {
                                console.error('复制失败:', error);
                                message.error('复制失败');
                              }
                            } else if (key === 'delete') {
                              setCurrentApp(app);
                              setDeleteModalVisible(true);
                            }
                          }}
                        >
                          <Menu.Item key="copy" icon={<CopyOutlined />}>
                            复制
                          </Menu.Item>
                          <Menu.Item key="delete" icon={<DeleteOutlined />} danger>
                            删除
                          </Menu.Item>
                        </Menu>
                      }
                      trigger={['hover']}
                    >
                      <span
                        onClick={(e) => e.stopPropagation()}
                        style={{
                          height: '40px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: 4,
                          width: '100%',
                          color: '#191919',
                          fontSize: 12,
                        }}
                      >
                        <img
                          src="/icons/more.svg"
                          alt="更多"
                          style={{ width: 12, height: 12, margin: '0 5px' }}
                        />
                        <span>更多</span>
                      </span>
                    </Dropdown>,
                  ]}
                >
                  {(app.type === '1' || app.appTypeDesc === '智能体') && (
                    <div
                      style={{
                        position: 'absolute',
                        right: 0,
                        top: 0,
                        width: 66,
                        zIndex: 10,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}
                    >
                      <span
                        style={{
                          color: '#fff',
                          fontSize: 13,
                          fontWeight: 500,
                          position: 'absolute',
                          right: 5,
                        }}
                      >
                        智能体
                      </span>
                      <img
                        src="http://**************:9010/hj-rule-engine/static/ai_agent_tag.svg"
                        alt="智能体"
                        style={{
                          width: 66,
                          height: 22,
                          borderRadius: '0 8px 0 0',
                          objectFit: 'cover',
                        }}
                      />
                    </div>
                  )}
                  {(app.type === '2' || app.appTypeDesc === '工作流') && (
                    <div
                      style={{
                        position: 'absolute',
                        right: 0,
                        top: 0,
                        width: 66,
                        zIndex: 10,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}
                    >
                      <span
                        style={{
                          color: '#fff',
                          fontSize: 13,
                          fontWeight: 500,
                          position: 'absolute',
                          right: 5,
                        }}
                      >
                        工作流
                      </span>
                      <img
                        src="http://**************:9010/hj-rule-engine/static/workflow_tag.svg"
                        alt="工作流"
                        style={{
                          width: 66,
                          height: 22,
                          borderRadius: '0 8px 0 0',
                          objectFit: 'cover',
                        }}
                      />
                    </div>
                  )}
                  <Card.Meta
                    avatar={<Avatar src={app.icon} size={48} />}
                    title={
                      <>
                        {app.appStatus === 1 && (
                          <span
                            style={{
                              background: '#E6EAED',
                              color: '#939CAF',
                              fontSize: 12,
                              borderRadius: 4,
                              padding: '2px 6px',
                              marginRight: 8,
                            }}
                          >草稿</span>
                        )}
                        {app.name}
                      </>
                    }
                    description={
                      <>
                        <div>{app.description}</div>
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            fontSize: 12,
                            marginTop: 5,
                            color: '#939CAF',
                          }}
                        >
                          <CalendarOutlined style={{ marginRight: 6 }} />
                          {app.modifyTime
                            ? dayjs(app.modifyTime).format('YYYY-MM-DD HH:mm:ss')
                            : '--'}
                        </div>
                      </>
                    }
                  />
                </Card>
              </div>
            ))}
          </>
        )}
      </div>
      {total > 0 && appList.length > 0 && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            &lt;
          </Button>
          {Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => (
            <Button
              key={i + 1}
              type={currentPage === i + 1 ? 'primary' : 'default'}
              style={{ margin: '0 8px' }}
              onClick={() => handlePageChange(i + 1)}
            >
              {i + 1}
            </Button>
          ))}
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === Math.ceil(total / pageSize)}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            &gt;
          </Button>
        </div>
      )}
      <CreateAppModal
        visible={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={handleCreateApp}
      />
      <EditAppModal
        visible={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleEditApp}
        initialValues={currentApp}
      />
      <DeleteConfirmModal
        visible={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        onOk={handleDeleteApp}
      />
    </PageContainer>
  );
};

const Home: React.FC = () => {
  return <CardList />;
};

export default Home;
