@sider-width: 260px;
@main-radius: 8px;
@main-bg: #f7fafd;
@white: #fff;
@shadow: 0 2px 8px #f0f1f2;

.data-page-container {
  display: flex;
  min-height: calc(100vh - 64px);
  background: none;
  border-radius: 0;
}

.data-page-sider {
  width: 300px;
  // margin: 16px 0 16px 16px;
  padding: 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 96px);
  background: none;
  border-radius: 0;
  box-shadow: none;
  padding: 20px;


  .ant-tree-node-content-wrapper{
    padding-inline:0;
  }
  .ant-segmented{
    margin-bottom: 20px;
    border-radius: 8px;
    .ant-segmented-item{
      width: 50%;
      border-radius: 6px;
    }
    .ant-segmented-item-label{
      // height: 32px;
      line-height: 32px;
    }
    // height: 32px;
  }
  .data-page-sider-search-bar{
    margin-bottom: 20px;
  }
}

.data-page-sider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 12px 16px;
  border-bottom: 1px solid #f0f0f0;

  .data-page-sider-header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
  }

  .data-page-sider-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    white-space: nowrap;
  }

  .data-page-tabs-bar .custom-filter-segmented {
    margin-bottom: 16px;
    width: 100%;
    display: flex;
    justify-content: flex-start;

    .ant-segmented {
        background: transparent;
        border: none;
        padding: 0;
        gap: 8px;
        min-width: 220px;
        height: 32px;
        box-shadow: none;

      .ant-segmented-group {
        width: 100%;
      }

      .ant-segmented-item {
          width: 50%;
          font-size: 14px;
          padding: 6px 16px;
          border-radius: 6px;
          background: #fff;
          border: 1px solid #e5e6eb;
          color: #666;
          font-weight: 400;
          height: 32px;
          line-height: 32px;
          min-width: auto;
          margin: 0;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

        &:hover {
          border-color: #0AAC79;
          color: #0AAC79;
        }
      }

      .ant-segmented-item-selected {
        background: #0AAC79;
        border-color: #0AAC79;
        color: #fff;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(10, 172, 121, 0.2);
        z-index: 1;

        &:hover {
          background: #0AAC79;
          border-color: #0AAC79;
          color: #fff;
        }
      }

      .ant-segmented-item:not(.ant-segmented-item-selected) {
        border: 1px solid #e5e6eb;
        color: #666;
        background: #fff;
        font-weight: 400;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
      }

      .ant-segmented-thumb {
        display: none;
      }
    }
  }

  .data-page-sider-add-btn {
    border: none;
    box-shadow: none;
    background: transparent;
    flex-shrink: 0;

    &:hover {
      background: #f5f5f5;
    }
  }
}

.data-page-sider-tree-wrapper {
  flex: 1;
  overflow: auto;
  // padding: 16px;
  background: none;
}

.data-page-sider-tree {
  background: none;

  .ant-tree-treenode {
    padding: 0;

    .ant-tree-switcher {
      width: 24px;
      height: 40px;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent !important;
      pointer-events: none;

      &:hover {
        background-color: transparent !important;
      }
    }

    .ant-tree-node-content-wrapper {
      height: 40px;
      line-height: 40px;
      padding: 0 8px;
      border-radius: 6px;

      &.ant-tree-node-selected {
        background-color: rgba(10, 172, 121, 0.06) !important;
        color: #0AAC79 !important;
      }
    }

    .ant-tree-node-content-wrapper {
      height: 40px;
      line-height: 40px;
      padding: 0 8px;
      transition: background-color 0.2s;

      &.ant-tree-node-selected {
        background-color: rgba(10, 172, 121, 0.06) !important;
        color: #0AAC79 !important;
      }
    }

    .ant-tree-title {
      display: flex;
      align-items: center;
      height: 100%;

      .anticon-ellipsis-outlined {
         transition: background-color 0.2s;

         &:hover {
           background-color: #f0f0f0 !important;
         }
       }
     }
  }

  .ant-tree-child-tree {
    .ant-tree-treenode {
      .ant-tree-node-content-wrapper {
        margin-left: 24px;
      }
    }
  }
}

.data-page-content {
  flex: 1;
  // margin: 16px 16px 16px 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  border-left: 1px solid #EAEFF3;
}

.data-page-content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px 0;
  // min-height: 56px;
  background: none;

  // 左侧搜索区
  > div:first-child {
    display: flex;
    align-items: center;
    gap: 12px; // 输入框和按钮间距
    .ant-input,
    .ant-btn {
      // height: 40px;
      // line-height: 40px;
      border-radius: 8px;
    }
  }

  // 右侧按钮区
  > div:last-child {
    display: flex;
    align-items: center;
    gap: 12px;
    .ant-btn {
      height: 40px;
      line-height: 40px;
      border-radius: 8px;
    }
  }
}

.data-page-content-batch-delete,
.data-page-content-batch-delete.ant-btn {
  margin-right: 8px;
}
.data-page-content-batch-delete:hover,
.data-page-content-batch-delete.ant-btn:hover,
.data-page-content-batch-delete.ant-btn:focus {
  background: #f5f5f5 !important;
  color: inherit !important;
  border-color: #e5e6eb !important;
}

.data-page-content-table-wrapper {
  flex: 1;
  overflow: auto;
  padding: 20px;
  background: none;
}

.data-page-content-table {
  // 斑马纹
  .ant-table-tbody > tr:nth-child(2n) > td {
    background: #fafbfc;
  }
  .ant-table-tbody > tr > td {
    transition: background 0.2s;
  }

  // 分页样式
  .ant-table-pagination {
    margin: 0 !important;
    padding: 16px 0 8px 0;
    background: none;
    border-radius: 0 0 8px 8px;
    text-align: right;
  }
  .ant-pagination {
    background: none;
  }
  .ant-pagination-item-active {
    background: #13C08A !important;
    border-color: #13C08A !important;
    color: #fff !important;
    border-radius: 4px;
    a{
      color: #fff
    }
  }
  .ant-pagination-item {
    border-radius: 4px;
    margin: 0 2px;
  }
  .ant-pagination-prev, .ant-pagination-next, .ant-pagination-jump-prev, .ant-pagination-jump-next {
    border-radius: 4px;
  }
  .ant-pagination-options {
    margin-left: 16px;
  }

  .ant-table-footer{
    background-color: #fff;
  }
}

.data-page-content-pagination-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
}

.data-page .ant-pro-page-container-children-container{
  padding: 0 !important;
}

.data-page-content-title-bar {
  // margin-bottom: 16px;
  padding: 20px 20px 0;
  .data-page-content-subtitle{
    padding: 10px 0 20px;
  }
}
.data-page-content-title {
  font-size: 16px;
  font-weight: 600;
  color: #222;
  line-height: 16px;
}
.data-page-content-subtitle {
  font-size: 12px;
  color: #b0b3bb;
  // line-height: 1.0;
  // margin-top: 2px;
}



.data-page-sider-search-bar {
  // padding: 16px 16px 0 16px;
  background: none;
.ant-input-affix-wrapper-sm{
  border-radius: 8px;
}
  .ant-input {
    height: 38px;
    border-radius: 8px;
  }
}
.required {
  color: #ff4d4f;
  margin-right: 2px;
}

.custom-pagination-bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // padding: 16px 16px 8px 16px;
  background: none;
  .custom-pagination-total {
    color: #222;
    font-size: 15px;
    margin-right: 8px;
  }
  .ant-select {
    margin-right: 8px;
  }
  .custom-pagination-btn {
    background: #fff;
    border: 1px solid #e5e6eb;
    border-radius: 6px;
    color: #222;
    font-size: 15px;
    padding: 0 12px;
    height: 32px;
    margin: 0 4px;
    cursor: pointer;
    transition: all 0.2s;
    &:disabled {
      color: #ccc;
      border-color: #f0f0f0;
      cursor: not-allowed;
      background: #fafbfc;
    }
  }
  .ant-pagination {
    margin: 0 8px !important;
    .ant-pagination-item {
      border-radius: 6px;
      margin: 0 2px;
      border: 1px solid #e5e6eb;
      background: #fff;
      color: #222;
      font-size: 15px;
      min-width: 32px;
      height: 32px;
      line-height: 32px;
      transition: all 0.2s;
    }
    .ant-pagination-item-active {
      background: #13C08A !important;
      border-color: #13C08A !important;
      color: #fff !important;
    }

    .ant-pagination-item-active:hover a{
      color: #fff !important;
    }
    .ant-pagination-jump-prev, .ant-pagination-jump-next {
      border-radius: 6px;
      border: 1px solid #e5e6eb;
      background: #fff;
      color: #222;
      min-width: 32px;
      height: 32px;
      line-height: 32px;
    }
    .ant-pagination-prev, .ant-pagination-next {
      border-radius: 6px;
      border: 1px solid #e5e6eb;
      background: #fff;
      color: #222;
      min-width: 32px;
      height: 32px;
      line-height: 32px;
    }
  }
}

.data-tree-node-title {
  position: relative;
  width: 100%;
  min-height: 32px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-right: 8px;
}

// 更多弹框菜单项样式，与树结构选中背景色和字体颜色保持一致
.ant-dropdown-menu {
  .ant-dropdown-menu-item {
    &:hover {
      background-color: rgba(10, 172, 121, 0.06) !important;
      color: #0AAC79 !important;
    }

    &:active {
      background-color: rgba(10, 172, 121, 0.06) !important;
      color: #0AAC79 !important;
    }
  }
}

.ant-tag.custom-status-parsed {
  color: #31B37D !important;
  background: rgba(49, 179, 125, 0.08) !important;
  border: none !important;
}
.ant-tag.custom-status-parsing {
  color: #E78F24 !important;
  background: rgba(231, 143, 36, 0.08) !important;
  border: none !important;
}
.ant-tag.custom-status-failed {
  color: #DE4B43 !important;
  background: rgba(222, 75, 67, 0.08) !important;
  border: none !important;
}

.data-page-sider-search-bar .ant-input,
.data-page-sider-search-bar .ant-input-affix-wrapper {
  background: #F6F7FB !important;
  border-radius: 8px;
  border: 1px solid #EAEFF3 ;
  // color: #939CAF !important;
}

.data-page-content-header .ant-input,
.data-page-content-header .ant-input-affix-wrapper {
  background: #F6F7FB !important;
  border-radius: 8px;
  border: 1px solid #EAEFF3 ;
  // color: #939CAF !important;
}

.data-page-sider-search-bar .ant-input:focus,
.data-page-sider-search-bar .ant-input-affix-wrapper-focused {
  border-color: #0AAC79 !important;
  box-shadow: 0 0 0 2px rgba(10, 172, 121, 0.06) !important;
}
