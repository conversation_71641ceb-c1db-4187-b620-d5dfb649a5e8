import SearchActionBar from '@/components/SearchActionBar';
import { EllipsisOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import {
  Button,
  Dropdown,
  Input,
  MenuProps,
  message,
  Modal,
  Pagination,
  Segmented,
  Select,
  Table,
  Tag,
  Tree,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useState } from 'react';
import './index.less';

interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
  [key: string]: any;
}

const rootCategoryKey = 'root';
const initialData = [
  {
    title: '所有数据目录',
    key: rootCategoryKey,
    children: [
      {
        title: '默认',
        key: '0-0',
        children: [
          { title: '国家通用法规', key: '0-0-0' },
          { title: '地方法规', key: '0-0-1' },
        ],
      },
      {
        title: '监管案例',
        key: '0-1',
        children: [
          { title: '正面案例', key: '0-1-0' },
          { title: '负面案例', key: '0-1-1' },
        ],
      },
    ],
  },
];

const tableData = [
  {
    key: '1',
    name: '实施工程建设强制性标准监督规定.pdf',
    status: '已解析',
    size: '723k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '2',
    name: '投标公司基本信息表.xlsx',
    status: '解析中',
    size: '873k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '3',
    name: '中华人民共和国招标投标法.docx',
    status: '已解析',
    size: '247k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '4',
    name: '实施工程建设强制性标准监督规定.pdf',
    status: '解析失败',
    size: '715k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '5',
    name: '投标公司基本信息表.xls',
    status: '已解析',
    size: '661k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '6',
    name: '中华人民共和国招标投标法.doc',
    status: '已解析',
    size: '445k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '7',
    name: '实施工程建设强制性标准监督规定.pptx',
    status: '已解析',
    size: '472k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '8',
    name: '投标公司基本信息表.xlsx',
    status: '已解析',
    size: '335k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '9',
    name: '中华人民共和国招标投标法.pdf',
    status: '已解析',
    size: '527k',
    time: '2025-04-16 08:50:04',
  },
  {
    key: '10',
    name: '实施工程建设强制性标准监督规定.docx',
    status: '已解析',
    size: '679k',
    time: '2025-04-16 08:50:04',
  },
];

const statusColor = {
  已解析: 'green',
  解析中: 'orange',
  解析失败: 'red',
};

// 根据文件名获取文件类型图标
const getFileTypeIcon = (fileName: string) => {
  const extension = fileName.toLowerCase().split('.').pop();
  switch (extension) {
    case 'pdf':
      return '/icons/pdf.svg';
    case 'xls':
    case 'xlsx':
      return '/icons/excel.svg';
    case 'doc':
    case 'docx':
      return '/icons/word.svg';
    case 'ppt':
    case 'pptx':
      return '/icons/ppt.svg';
    default:
      return '/icons/file.svg';
  }
};

const columns: ColumnsType<{
  key: string;
  name: string;
  status: string;
  size: string;
  time: string;
}> = [
  {
    title: '序号',
    dataIndex: 'key',
    width: 60,
    align: 'center',
    render: (text: string, _: any, index: number) => index + 1,
  },
  {
    title: '文件名称',
    dataIndex: 'name',
    width: 260,
    render: (text: string) => (
      <span style={{ display: 'flex', alignItems: 'center' }}>
        <img
          src={getFileTypeIcon(text)}
          alt="file type"
          style={{ width: 22, height: 22, marginRight: 8 }}
        />
        {text}
      </span>
    ),
  },
  {
    title: '解析状态',
    dataIndex: 'status',
    width: 100,
    align: 'center',
    render: (status: string) => (
      status === '已解析' ? (
        <Tag className="custom-status-parsed">{status}</Tag>
      ) : status === '解析中' ? (
        <Tag className="custom-status-parsing">{status}</Tag>
      ) : status === '解析失败' ? (
        <Tag className="custom-status-failed">{status}</Tag>
      ) : (
        <Tag color={statusColor[status as keyof typeof statusColor]}>{status}</Tag>
      )
    ),
  },
  {
    title: '文件大小',
    dataIndex: 'size',
    width: 100,
    align: 'center',
  },
  {
    title: '导入时间',
    dataIndex: 'time',
    width: 180,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    align: 'center',
    render: () => (
      <>
        <a style={{ marginRight: 16, color: '#0E77D1' }}>详情</a>
        <a style={{ color: 'red' }}>删除</a>
      </>
    ),
  },
];

// 菜单项根据节点类型动态生成
const getMenuItems = (node: any): MenuProps['items'] => {
  if (node.key === rootCategoryKey) {
    return [{ label: '新增子分类', key: 'add' }];
  }
  return [
    { label: '新增子分类', key: 'add' },
    { label: '编辑', key: 'edit' },
    { label: '删除', key: 'delete' },
  ];
};

const withRawTitle = (data: TreeNode[]): TreeNode[] =>
  data.map((item) => ({
    ...item,
    rawTitle: item.title,
    children: item.children ? withRawTitle(item.children) : undefined,
  }));

const DataList: React.FC = () => {
  const [treeData] = useState(withRawTitle(initialData));
  const [subModalVisible, setSubModalVisible] = useState(false);
  const [parentCategory, setParentCategory] = useState('');
  const [subCategoryName, setSubCategoryName] = useState('');
  const [subCategoryDesc, setSubCategoryDesc] = useState('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(treeData[0]?.title || '');
  const [activeTab, setActiveTab] = useState('unstructured');
  const [categoryKeyword, setCategoryKeyword] = useState('');
  const [currentNode, setCurrentNode] = useState<any>(null);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([treeData[0]?.key || '']);
  const total = 23843;
  const pageCount = Math.ceil(total / pageSize);

  const fileIcon = '/icons/file.svg';
  // const addIcon = '/icons/data_add.svg';

  // 右键菜单点击
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    if (key === 'add') {
      setParentCategory(currentNode?.title || '');
      setSubModalVisible(true);
    } else if (key === 'edit') {
      message.info('编辑功能待实现');
    } else if (key === 'delete') {
      message.info('删除功能待实现');
    }
  };

  // 高亮搜索关键词
  const highlightKeyword = (text: string, keyword: string) => {
    if (!keyword) return text;
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);
    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} style={{ backgroundColor: '#ffeb3b', color: '#000' }}>
          {part}
        </span>
      ) : (
        part
      ),
    );
  };

  // 渲染树节点，添加右键菜单和自定义图标
  const renderTitle = (nodeData: any) => (
    <div
      className="data-tree-node-title"
        style={{
          display: 'flex',
          alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
        minHeight: 32,
        boxSizing: 'border-box',
        paddingRight: 8,
      }}
    >
      <span style={{ display: 'flex', alignItems: 'center' }}>
        <img src={fileIcon} alt="icon" style={{ width: 18, height: 18, marginRight: 6 }} />
        <span>{highlightKeyword(nodeData.title, categoryKeyword)}</span>
      </span>
      <Dropdown
        menu={{
          items: getMenuItems(nodeData),
          onClick: handleMenuClick,
        }}
        trigger={['click']}
        placement="bottomRight"
        onOpenChange={(open) => {
          if (open) setCurrentNode(nodeData);
        }}
      >
        {selectedKeys.includes(nodeData.key) && (
          <span onClick={(e) => e.stopPropagation()} onMouseDown={(e) => e.stopPropagation()}>
            <EllipsisOutlined
              className="data-tree-node-ellipsis"
              style={{
                marginLeft: 8,
                cursor: 'pointer',
                fontSize: '14px',
                color: '#666',
                backgroundColor: '#FFFFFF',
                borderRadius: '8px',
                width: '30px',
                height: '30px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: '1px solid #EAEFF3',
              }}
            />
          </span>
        )}
      </Dropdown>
    </div>
  );

  // 递归映射树形数据，为所有层级的节点添加高亮
  const mapTreeDataWithHighlight = (nodes: any[]): any[] => {
    return nodes.map((node) => ({
      ...node,
      title: renderTitle(node),
      children: node.children ? mapTreeDataWithHighlight(node.children) : undefined,
    }));
  };

  const handleQuery = () => {
    // 查询逻辑
  };

  const handleReset = () => {
    setSearchKeyword('');
    // 重置逻辑
  };

  const handleTreeSelect = (selectedKeys: React.Key[], info: any) => {
    setSelectedCategory(info.node.rawTitle || '');
    setSelectedKeys(selectedKeys);

    // 处理展开收起逻辑
    const nodeKey = info.node.key;
    if (info.node.children && info.node.children.length > 0) {
      if (expandedKeys.includes(nodeKey)) {
        // 如果已展开，则收起
        setExpandedKeys(expandedKeys.filter((key) => key !== nodeKey));
      } else {
        // 如果未展开，则展开
        setExpandedKeys([...expandedKeys, nodeKey]);
      }
    }
  };

  // 目录搜索过滤
  // 搜索匹配逻辑：递归查找匹配的节点，支持全层级匹配
  const searchInTree = (nodes: any[], keyword: string): any[] => {
    if (!keyword) return nodes;

    const lowerKeyword = keyword.toLowerCase();

    return nodes
      .map((node) => {
        // 检查当前节点是否匹配
        const titleMatch = node.title.toLowerCase().includes(lowerKeyword);

        // 递归搜索子节点
        let matchedChildren: any[] = [];
        if (node.children) {
          matchedChildren = searchInTree(node.children, keyword);
        }

        // 检查是否有任何子孙节点匹配（深度搜索）
        const hasMatchingDescendants = (nodeList: any[]): boolean => {
          return nodeList.some((child) => {
            const childMatches = child.title.toLowerCase().includes(lowerKeyword);
            const grandChildMatches = child.children
              ? hasMatchingDescendants(child.children)
              : false;
            return childMatches || grandChildMatches;
          });
        };

        const hasDescendantMatch = node.children ? hasMatchingDescendants(node.children) : false;

        // 如果当前节点匹配、有匹配的子节点或有匹配的子孙节点，则保留该节点
        if (titleMatch || matchedChildren.length > 0 || hasDescendantMatch) {
          return {
            ...node,
            children: matchedChildren.length > 0 ? matchedChildren : node.children,
          };
        }

        return null;
      })
      .filter(Boolean);
  };

  // 获取所有匹配节点的key，用于自动展开（支持全层级匹配）
  const getMatchedKeys = (nodes: any[], keyword: string): string[] => {
    if (!keyword) return [];

    const keys: string[] = [];
    const lowerKeyword = keyword.toLowerCase();

    const traverse = (nodeList: any[], parentKeys: string[] = []) => {
      nodeList.forEach((node) => {
        const currentPath = [...parentKeys, node.key];

        // 检查当前节点是否匹配
        const nodeMatches = node.title.toLowerCase().includes(lowerKeyword);

        // 检查是否有任何子孙节点匹配
        const hasMatchingDescendants = (children: any[]): boolean => {
          return children.some((child) => {
            const childMatches = child.title.toLowerCase().includes(lowerKeyword);
            const grandChildMatches = child.children
              ? hasMatchingDescendants(child.children)
              : false;
            return childMatches || grandChildMatches;
          });
        };

        const hasDescendantMatch = node.children ? hasMatchingDescendants(node.children) : false;

        // 如果当前节点匹配或有子孙节点匹配，展开到当前节点的所有父节点
        if (nodeMatches || hasDescendantMatch) {
          // 添加所有父节点的key（用于展开路径）
          parentKeys.forEach((parentKey) => {
            if (!keys.includes(parentKey)) {
              keys.push(parentKey);
            }
          });

          // 如果当前节点有子节点且有匹配的子孙节点，也要展开当前节点
          if (node.children && hasDescendantMatch) {
            if (!keys.includes(node.key)) {
              keys.push(node.key);
            }
          }
        }

        // 递归处理子节点
        if (node.children) {
          traverse(node.children, currentPath);
        }
      });
    };

    traverse(nodes);
    return [...new Set(keys)]; // 去重
  };

  const filteredTreeData = searchInTree(treeData, categoryKeyword);

  // 当搜索关键词变化时，自动展开匹配的节点
  React.useEffect(() => {
    if (categoryKeyword) {
      const matchedKeys = getMatchedKeys(treeData, categoryKeyword);
      setExpandedKeys(matchedKeys);
    }
  }, [categoryKeyword]);

  const handleAddSubCategory = () => {
    if (!subCategoryName) {
      message.warning('请输入类型名称');
      return;
    }

    setSubModalVisible(false);
    setSubCategoryName('');
    setSubCategoryDesc('');
  };

  return (
    <PageContainer ghost className="data-page">
      <div className="data-page-container">
        {/* 左侧树形结构 */}
        <div className="data-page-sider">
          <Segmented
            value={activeTab}
            onChange={(value) => setActiveTab(value as string)}
            options={[
              { label: '非结构化数据', value: 'unstructured' },
              { label: '结构化数据', value: 'structured' },
            ]}
            className="custom-filter-segmented data-page-tabs-bar"
            size="middle"
          />

          <div className="data-page-sider-search-bar">
            <Input
              placeholder="请输入类目名称"
              allowClear
              value={categoryKeyword}
              onChange={(e) => {
                const value = e.target.value;
                setCategoryKeyword(value);
                // 如果清空搜索，恢复默认展开状态
                if (!value) {
                  setExpandedKeys([]);
                }
              }}
              size="small"
              prefix={<img src="/icons/search.svg" alt="search" style={{ width: 18, height: 18, marginRight: 4 }} />}
            />
          </div>
          <div className="data-page-sider-tree-wrapper">
            <Tree
              blockNode
              expandedKeys={expandedKeys}
              onExpand={setExpandedKeys}
              selectedKeys={selectedKeys}
              treeData={mapTreeDataWithHighlight(filteredTreeData)}
              onSelect={handleTreeSelect}
              className="data-page-sider-tree"
            />
          </div>
        </div>
        {/* 右侧内容区域 */}
        <div className="data-page-content">
          <div className="data-page-content-title-bar">
            <div className="data-page-content-title">{selectedCategory}</div>
            <div className="data-page-content-subtitle">
              该主题收录工程建设类项目的招标、投标、评标、定标等信息。
            </div>
          </div>
          <div className="data-page-content-header">
            <div>
              <SearchActionBar
                searchKeyword={searchKeyword}
                onKeywordChange={setSearchKeyword}
                onQuery={handleQuery}
                onReset={handleReset}
                onPressEnter={handleQuery}
                prefix={<img src="/icons/search.svg" alt="search" style={{ width: 18, height: 18, marginRight: 4 }} />}
              />
            </div>
            <div>
              <Button className="data-page-content-batch-delete" style={{ height: 40 }}>
                <img src="/icons/remove.svg" alt="remove" style={{ width: 18, height: 18 }} />
                批量删除
              </Button>
              <Button type="primary" style={{ height: 40 }}>
                <img src="/icons/add_02.svg" alt="add" style={{ width: 18, height: 18, filter: 'brightness(0) invert(1)' }} />
                导入数据
              </Button>
            </div>
          </div>
          <div className="data-page-content-table-wrapper">
            <Table
              columns={columns}
              dataSource={tableData}
              bordered
              size="middle"
              rowSelection={{}}
              className="data-page-content-table"
              pagination={false}
              footer={() => (
                <div className="custom-pagination-bar">
                  <span className="custom-pagination-total">共{total}条</span>
                  <Select
                    value={pageSize}
                    onChange={(value) => {
                      setPageSize(value);
                      setCurrent(1);
                    }}
                    style={{ width: 90, margin: '0 8px' }}
                    options={[
                      { value: 10, label: '10条/页' },
                      { value: 20, label: '20条/页' },
                      { value: 50, label: '50条/页' },
                      { value: 100, label: '100条/页' },
                    ]}
                  />
                  <button
                    type="button"
                    className="custom-pagination-btn"
                    disabled={current === 1}
                    onClick={() => setCurrent(1)}
                  >
                    首页
                  </button>
                  <Pagination
                    current={current}
                    pageSize={pageSize}
                    total={total}
                    onChange={setCurrent}
                    showSizeChanger={false}
                    itemRender={(page, type, originalElement) => {
                      if (type === 'page') return originalElement;
                      if (type === 'prev') return originalElement;
                      if (type === 'next') return originalElement;
                      if (type === 'jump-prev' || type === 'jump-next') return originalElement;
                      return originalElement;
                    }}
                    showQuickJumper={false}
                    showLessItems={false}
                  />
                  <button
                    type="button"
                    className="custom-pagination-btn"
                    disabled={current === pageCount}
                    onClick={() => setCurrent(pageCount)}
                  >
                    尾页
                  </button>
                </div>
              )}
            />
          </div>
        </div>
      </div>

      <Modal
        title="新增子分类"
        open={subModalVisible}
        onOk={handleAddSubCategory}
        onCancel={() => setSubModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <div className="modal-form-label">父级目录</div>
        <Input value={parentCategory} disabled />
        <div className="modal-form-label">
          <span className="required">*</span> 类型名称
        </div>
        <Input
          maxLength={15}
          value={subCategoryName}
          onChange={(e) => setSubCategoryName(e.target.value)}
          placeholder="请输入"
          suffix={<span>{subCategoryName.length}/15</span>}
        />
        <div className="modal-form-label">类型描述</div>
        <Input.TextArea
          maxLength={100}
          value={subCategoryDesc}
          onChange={(e) => setSubCategoryDesc(e.target.value)}
          placeholder="请输入"
          rows={4}
          style={{ resize: 'none' }}
        />
        <div className="modal-form-count">{subCategoryDesc.length}/100</div>
      </Modal>
    </PageContainer>
  );
};

export default DataList;
