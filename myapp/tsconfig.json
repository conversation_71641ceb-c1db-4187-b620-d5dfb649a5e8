{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "importHelpers": true,
    "jsx": "preserve",
    "esModuleInterop": true,
    "sourceMap": true,
    "baseUrl": "./",
    "skipLibCheck": true,
    "experimentalDecorators": true,
    "strict": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "@@/*": [
        "./src/.umi/*"
      ],
      "@@test/*": [
        "./src/.umi-test/*"
      ]
    }
  },
  // "include": ["./**/*.d.ts", "./**/*.ts", "./**/*.tsx"]
  "include": [
    "src",
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "public",
    "src/**/*.test.ts",
    "src/**/*.test.tsx",
    "src/**/*.d.ts"
  ]
}