﻿export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/home',
    name: '应用',
    icon: '/icons/application.svg',
    activeIcon: '/icons/application_active.svg',
    component: './Home',
  },
  {
    path: '/model',
    name: '模型',
    icon: '/icons/model.svg',
    activeIcon: '/icons/model_active.svg',
    component: './Model',
  },
  {
    path: '/component',
    name: '组件',
    icon: '/icons/component.svg',
    activeIcon: '/icons/component_active.svg',
    component: './Component',
  },
  {
    path: '/mcp',
    name: 'MCP',
    icon: '/icons/MCP.svg',
    activeIcon: '/icons/MCP_active.svg',
    component: './MCP',
  },
  {
    path: '/data',
    name: '数据',
    icon: '/icons/data.svg',
    activeIcon: '/icons/data_active.svg',
    component: './Data',
  },
  {
    path: '/knowledge',
    name: '知识',
    icon: '/icons/knowledge.svg',
    activeIcon: '/icons/knowledge_active.svg',
    component: './Knowledge',
  },
  {
    path: '/',
    redirect: '/home',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
