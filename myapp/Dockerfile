# 第一阶段：构建阶段
FROM node:20-alpine AS builder

WORKDIR /app

# 优先复制包管理文件以利用Docker缓存
COPY package.json yarn.lock* ./

# 使用国内镜像源加速安装（可选）
RUN yarn config set registry https://registry.npmmirror.com/ \
    && yarn install --frozen-lockfile --network-timeout 1000000

# 复制项目文件
COPY . .

# 执行构建
RUN yarn build

RUN ls -la /app/dist && find /app/dist -type f

# 第二阶段：生产镜像
FROM nginx:1.28-alpine

# 复制nginx配置（需要提前准备nginx.conf）
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 从构建阶段复制产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动nginx服务
CMD ["nginx", "-g", "daemon off;"]
